#include <chrono>
#include <thread>

#include "inverter_meanwell.hpp"
#include "config.hpp"
#include "can.hpp"
#include "logging.hpp"

void request_meanwell_inverter_information() {

    CanDevice::getPowerCan()->resetSendDataBuffer();
    CanDevice::getPowerCan()->getSendDataBuffer()[0] = INVERTER_VOLTAGE__CMD_HIGH;
    CanDevice::getPowerCan()->getSendDataBuffer()[1] = INVERTER_VOLTAGE__CMD_LOW;

    CanDevice::getPowerCan()->sendLastPopulatedData(
            MEANWELL_REQUEST_FRAME_ID,
            MEANWELL_INVERTER_DATA_LENGTH,
            true,
            false // Data frame, not remote transmission request
    );
#ifdef TESTING_MODE
    Log("Inverter voltage request sent");
    Log("CAN ID: 0x");
    Log(MEANWELL_REQUEST_FRAME_ID);
    Log(" Data: ");
    for (int i = 0; i < MEANWELL_INVERTER_DATA_LENGTH; i++) {
        Log(CanDevice::getPowerCan()->getSendDataBuffer()[i]);
        Log(" ");
    }
    Log("");
#endif

    std::this_thread::sleep_for(CAN_MSG_DELAY);
}

auto convert_meanwell_data_to_float(
    const uint8_t high_byte,
    const uint8_t low_byte,
    float scaling_factor
    ) -> float
{
    // Swap bytes as per Meanwell protocol (low and high bytes need to be swapped)
    uint16_t rawValue = (low_byte << 8) | high_byte;

    // Convert to decimal and scale using the provided scaling factor
    float result = (float)rawValue * scaling_factor;

    return result;
}


// Calculate battery capacity percentage from voltage
auto calculate_battery_capacity_from_voltage(float voltage) -> float
{
    // Clamp voltage to valid range
    if (voltage <= MEANWELL_BATTERY_MIN_VOLTAGE) {
        return 0.0f;
    }
    if (voltage >= MEANWELL_BATTERY_MAX_VOLTAGE) {
        return 100.0f;
    }

    // Linear mapping: (voltage - min) / (max - min) * 100
    float capacity = (
            (voltage - MEANWELL_BATTERY_MIN_VOLTAGE)
            / (
                MEANWELL_BATTERY_MAX_VOLTAGE
                - MEANWELL_BATTERY_MIN_VOLTAGE
            )
    ) * 100.0f;

    return capacity;
}

void parse_meanwell_inverter_message(
    const uint32_t can_id,
    CanDevice::RecvDataBuffer_t message,
    float msg_actuation_battery_ros[3]
    )
{
    static bool initialized = false;
    if (!initialized) {
        msg_actuation_battery_ros[0] = 0.0f; // Voltage
        msg_actuation_battery_ros[1] = 0.0f; // Current (always 0 for inverter)
        msg_actuation_battery_ros[2] = 0.0f; // Battery capacity (calculated from voltage)
        initialized = true;
    }
    Log("Received CAN ID: 0x");
    Log(can_id);
    Log(" Data: ");
    for (int i = 0; i < 8; i++) {
        Log(message[i]);
        Log(" ");
    }

    // Verify this is a Meanwell response frame
    if (can_id != MEANWELL_RESPONSE_FRAME_ID) {
        Log("Not a Meanwell response frame");
        return;
    }
    uint8_t data_high = message[2];
    uint8_t data_low = message[3];

    float convertedValue = convert_meanwell_data_to_float(
        data_high,
        data_low,
        INVERTER_VOLTAGE__SCALING_FACTOR
    );

    msg_actuation_battery_ros[0] = convertedValue;
    msg_actuation_battery_ros[1] = 0.0f;
    msg_actuation_battery_ros[2] = calculate_battery_capacity_from_voltage(convertedValue);

    Log("Meanwell Inverter Voltage: ");
    Log(convertedValue);
    Log(" V");

    Log("Meanwell Inverter Current: ");
    Log(msg_actuation_battery_ros[1]);
    Log(" A (default)");

    Log("Meanwell Inverter Battery Capacity: ");
    Log(msg_actuation_battery_ros[2]);
    Log(" % (calculated from voltage)");
}

void disable_meanwell_inverter(bool disable) {
    CanDevice::getPowerCan()->resetSendDataBuffer();
    CanDevice::getPowerCan()->getSendDataBuffer()[0] = INVERTER_OPERATION_CMD_HIGH;
    CanDevice::getPowerCan()->getSendDataBuffer()[1] = INVERTER_OPERATION_CMD_LOW; 
    CanDevice::getPowerCan()->getSendDataBuffer()[2] = disable ? INVERTER_DISABLE : INVERTER_ENABLE;
    CanDevice::getPowerCan()->getSendDataBuffer()[3] = ZeroValue;

    CanDevice::getPowerCan()->sendLastPopulatedData(
        MEANWELL_REQUEST_FRAME_ID,
        4,
        true,
        false
    );

    std::this_thread::sleep_for(CAN_MSG_DELAY);
}
