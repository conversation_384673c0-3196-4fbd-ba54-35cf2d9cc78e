#pragma once
#include <stdbool.h>
#include <stdint.h>

#include "config.hpp"
#include "can.hpp"

#define JKBMS 1
#define DALYBMS 2

// Include both battery and inverter headers
#if BATTERY_BMS == JKBMS
#include "battery_jkbms.hpp"
#elif BATTERY_BMS == DALYBMS
#include "battery_dalybms.hpp"
#else
#error "Battery BMS Not Defined"
#endif // BATTERY_BMS

#include "inverter_meanwell.hpp"

void parse_battery_message(
    const uint32_t can_id,
    CanDevice::RecvDataBuffer_t message,
    float msg_actuation_battery_ros[3]
);
void request_battery_information_legacy();
