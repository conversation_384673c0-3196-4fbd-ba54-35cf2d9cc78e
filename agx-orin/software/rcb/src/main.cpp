#include <cstdarg>
#include <cstddef>
#include <cstdint>
#include <chrono>
#include <thread>
#include <array>

// External Libraries for peripherals
#ifndef __linux__
#include <NativeEthernet.h>
#include <SPI.h>
#include <micro_ros_platformio.h>
#else
#define LED_BUILTIN 13
#endif
// uROS Libraries
#include <rcl/publisher.h>
#include <rcl/subscription.h>
#include <rcl/error_handling.h>
#include <rcl/rcl.h>
#include <rclc/executor.h>
#include <rclc/rclc.h>
#include <rmw_microros/rmw_microros.h>
#include <rosidl_runtime_c/string_functions.h>
#include <rosidl_runtime_c/primitives_sequence_functions.h>

#include <std_msgs/msg/float32.h>
#include <std_msgs/msg/float32_multi_array.h>
#include <std_msgs/msg/int32.h>
#include <std_msgs/msg/int32_multi_array.h>
#include <std_msgs/msg/u_int16.h>
#include <std_msgs/msg/u_int16_multi_array.h>
#include <std_msgs/msg/u_int8.h>
#include <sensor_msgs/msg/joint_state.h>

// Custom Headers
#include "rcb_pinout.hpp"
#include "battery.hpp"
#include "power_system.hpp"
#include "config.hpp"
#include "object_dictionary.hpp"
#include "ros_comms.hpp"
#include "tower_light.hpp"
#include "uros.hpp"
#include "utils.hpp"
#include "wheel_control.hpp"
#include "liftkit.hpp"
#include "custom_msgs/srv/move_lift.h"
#include "custom_msgs/msg/encoder_data.h"
#include "custom_msgs/msg/power_readings.h"
#include "relay_control.hpp"
#include "can.hpp"
#include "logging.hpp"

#ifndef __IMXRT1062__
#endif

#ifdef TESTING_MODE
#define MODE "Testing Mode"
#elif DEBUG
#define MODE "Debug Mode"
#else
#define MODE "Release Mode"
#endif

// ROS Configurations
#define NODE_NAME "amr_control_board_node"
#define AMR_SET_STATE_TOPIC "amr/set_state"
#define AMR_GET_STATE_TOPIC "amr/get_state"
#define RAW_ENCODER_PUBLISHER_TOPIC "amr/raw_encoder"
#define MOTOR_ERROR_PUBLISHER_TOPIC "amr/motor_error"
#define REQUIRED_TRAVEL_MOTOR_1_SPEED_SUBSCRIBER_TOPIC                         \
  "amr/required_travel_motor_speed"
#define REQUIRED_STEER_MOTOR_1_ANGLE_SUBSCRIBER_TOPIC                          \
  "amr/required_steer_motor_angle"
#define WHEEL_POSE_CONTROL_TOPIC "amr/wheel_pose_control"
#define AMR_POWER_TOPIC "amr/power"
#define POWER_SYSTEM_ACTUATION_BATTERY_STATUS_TOPIC                            \
  "power_system/get_power_readings"
#define HEARTBEAT_TOPIC "heartbeat"

#define LIFTKIT_SERVICE_TOPIC "/move_lift"
#define LIFTKIT_JOINT_STATE_TOPIC "/joint_states"
#define LIFTKIT_JOINT_STATE_NUM_JOINTS 2
#define LIFTKIT_JOINT_STATE_NAME_0 "liftkit_top"
#define LIFTKIT_JOINT_STATE_NAME_1 "liftkit_mid"

#define RCCHECK(fn)                                                            \
  {                                                                            \
    rcl_ret_t temp_rc = fn;                                                    \
    if ((temp_rc != RCL_RET_OK)) {                                             \
      error_loop();                                                            \
    }                                                                          \
  }
#define RCSOFTCHECK(fn)                                                        \
  {                                                                            \
    rcl_ret_t temp_rc = fn;                                                    \
    if ((temp_rc != RCL_RET_OK)) {                                             \
    }                                                                          \
  }

// Wheel Parameters
#define FRONT_WHEEL_TRAVEL_MOTOR_NODE_ID        0x01
#define FRONT_WHEEL_STEER_MOTOR_NODE_ID         0x02
#define REAR_LEFT_WHEEL_TRAVEL_MOTOR_NODE_ID    0x03
#define REAR_LEFT_WHEEL_STEER_MOTOR_NODE_ID     0x04
#define REAR_RIGHT_WHEEL_TRAVEL_MOTOR_NODE_ID   0x05
#define REAR_RIGHT_WHEEL_STEER_MOTOR_NODE_ID    0x06
#define LIFTKIT_STEER_MOTOR_NODE_ID             0x07

// Frequencies for communications (all times are in mS)
#define ERROR_TIME 500                  // 2Hz
#define ENCODER_TIME 50                  // 20Hz
#define BATTERY_TIME 10 * 1000          // 0.1Hz
#define HEARTBEAT_TIME_MS 1000          // 1Hz
#define AMR_GET_STATE_TIMER_MS 50       // 20Hz
#define LIFTKIT_HEIGHT_PUBLISH_TIME 50  // 20Hz
#define SYNC_TIME                   2000

using namespace std::chrono_literals;
#define UR10_POWERON_PULSE_TIME 500ms

// ROS Datatypes
bool g_wheelPoseControlMsgRecieved = false;
bool g_flagStateChangeRequested = false;
std_msgs__msg__Float32MultiArray wheel_pose_control_msg;
std_msgs__msg__UInt8 amr_power_msg;
std_msgs__msg__UInt8 msg_amr_set_state;
std_msgs__msg__UInt8 msg_amr_get_state;
std_msgs__msg__UInt8 msg_incoming_heartbeat;
std_msgs__msg__UInt8 msg_outgoing_heartbeat;
custom_msgs__msg__PowerReadings msg_power_system_readings;
std_msgs__msg__UInt16MultiArray motor_error_msg;
sensor_msgs__msg__JointState msg_liftkit_joint_state;

custom_msgs__msg__EncoderData raw_encoder_msg;

const int32_t LIFTKIT_HOMING_OFFSET_MM       = 10;     //mm. -73 for v1.2
const int32_t FRONT_STEER_MOTOR_OFFSET       = 42740;   // 65081 for v1.2
const int32_t REAR_LEFT_STEER_MOTOR_OFFSET   = 28120;   // 63020 for v1.2
const int32_t REAR_RIGHT_STEER_MOTOR_OFFSET  = 56520;   // 19959 for v1.2

wheelControl frontWheel(
        1,
        FRONT_WHEEL_TRAVEL_MOTOR_NODE_ID,
        FRONT_WHEEL_STEER_MOTOR_NODE_ID,
        FRONT_STEER_MOTOR_OFFSET,
        1000
);
wheelControl rearLeftWheel(
        2,
        REAR_LEFT_WHEEL_TRAVEL_MOTOR_NODE_ID,
        REAR_LEFT_WHEEL_STEER_MOTOR_NODE_ID,
        REAR_LEFT_STEER_MOTOR_OFFSET,
        1000
);
wheelControl rearRightWheel(
        3,
        REAR_RIGHT_WHEEL_TRAVEL_MOTOR_NODE_ID,
        REAR_RIGHT_WHEEL_STEER_MOTOR_NODE_ID,
        REAR_RIGHT_WHEEL_STEER_MOTOR_NODE_ID,
        1000
);
std::array<wheelControl*, 3> g_wheels = {&frontWheel, &rearLeftWheel, &rearRightWheel};

LiftkitControl liftkit(
        LIFTKIT_STEER_MOTOR_NODE_ID,
        LIFTKIT_HOMING_OFFSET_MM,
        1000,
        LIFTKIT_MOTOR_ACC_DEC_CONVERSION_FACTOR,
        LIFTKIT_MOTOR_POS_VEL_CONVERSION_FACTOR
);


ActiveLowRelay motorSSR(MOTOR_SSR);
ActiveLowRelay actuationPowerSSR(ACTUATION_POWER_CONTACTOR_SSR);
ActiveHighRelay armPoweronSSR(UR10_SSR_ON);
ActiveLowRelay armPoweroffSSR(UR10_SSR_OFF);    // Presently not used
ActiveHighRelay ledbuiltin(LED_BUILTIN);


int64_t error_read_time = 0;
int64_t encoder_read_time = 0;
uint64_t error_counter = 0;
uint32_t battery_read_time = 0;
uint32_t liftkit_encoder_read_time = 0;
uint32_t sync_time = 0;

// Function Definition
void encoder_timer_callback(rcl_timer_t *timer, int64_t last_call_time);
void callback_timer_power_system_battery_reading(rcl_timer_t *timer,
                                                 int64_t last_call_time);
void callback_timer_amr_get_state(rcl_timer_t *timer, int64_t last_call_time);
void wheel_pose_control_callback(const void *msgin);
void amr_power_callback(const void *msgin);
void callback_amr_set_state(const void *msgin);
void callback_outgoing_heartbeat_timer(rcl_timer_t *timer,
                                       int64_t last_call_time);
void callback_incoming_heartbeat(const void *msgin);
void callback_liftkit_joint_state_pub();

// Error handle loop
void error_loop() {
  Log("uROS Error Has occured!!");
  while (1) {
    std::this_thread::sleep_for(50ms);
    ledbuiltin.toggle();
  }
}

auto initializeIo() -> void
{
    motorSSR.disable();
    actuationPowerSSR.enable();
    armPoweronSSR.enable();
    std::this_thread::sleep_for(UR10_POWERON_PULSE_TIME);
    armPoweronSSR.disable();
    set_tower_light_state(eTowerLightState::UNINITIALIZED_MODE);
    set_tower_light_state(eTowerLightState::INITIALIZING_MODE);
    ledbuiltin.enable();
    set_tower_light_state(eTowerLightState::INITIALIZED_MODE);

    power_system_init();
}

void initialize_amr() {
  Log("Starting AMR");
  motorSSR.enable();
  std::this_thread::sleep_for(10000ms);
  frontWheel.m_initializeWheel(true, false);
  rearLeftWheel.m_initializeWheel(false, false);
  rearRightWheel.m_initializeWheel(false, false);
  set_tower_light_state(eTowerLightState::ACTIVE_MODE);
  Log("AMR Initialized");
}

void amr_engage_brakes() {
  Log("Estop Requested");
  for (auto * wheel : g_wheels) {
    wheel->m_emergencyStopWheel();
  }
}

enum class amrState {
  UNINITIALIZED = 0x00,
  INITIALIZE = 0x01,
  INITIALIZING = 0x02,
  ACTIVE = 0x03,
  ERROR = 0x04,
  UNINITIALIZE = 0x05,
  UNINITIALIZING = 0x06,
};

amrState amr_state = amrState::UNINITIALIZED;
// ------------------------------------------------------ //
// Publishers
// ------------------------------------------------------ //
Publisher * g_rawEncoderPub;
Publisher * g_motorErrorPub;
Publisher * g_actuationBatteryPub;
Publisher * g_heartbeatPub;
Publisher * g_statePub;
Publisher * g_liftJointPosePub;

void setup_all_publishers() {
    g_rawEncoderPub = ROSConnectionManager::getInstance().addPub(
        ROSIDL_GET_MSG_TYPE_SUPPORT(custom_msgs, msg, EncoderData),
        RAW_ENCODER_PUBLISHER_TOPIC
    );

    // Set Encoder publisher
    custom_msgs__msg__EncoderData__init(&raw_encoder_msg);

    g_motorErrorPub = ROSConnectionManager::getInstance().addPub(
        ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt16MultiArray),
        MOTOR_ERROR_PUBLISHER_TOPIC,
        false
    );
    motor_error_msg.data.capacity = 6;
    motor_error_msg.data.size = 6;
    static uint16_t motorErrorMessage[6];
    motor_error_msg.data.data = motorErrorMessage;

    g_actuationBatteryPub = ROSConnectionManager::getInstance().addPub(
        ROSIDL_GET_MSG_TYPE_SUPPORT(custom_msgs, msg, PowerReadings),
        POWER_SYSTEM_ACTUATION_BATTERY_STATUS_TOPIC
    );

    custom_msgs__msg__PowerReadings__init(&msg_power_system_readings);

    g_heartbeatPub = ROSConnectionManager::getInstance().addPub(
        ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt8),
        HEARTBEAT_TOPIC
    );

    msg_outgoing_heartbeat.data =
        (uint8_t)ROS_DEVICE_HEARTBEAT::RCB_ROS_HEARTBEAT_ID;

    g_statePub = ROSConnectionManager::getInstance().addPub(
        ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt8),
        AMR_GET_STATE_TOPIC
    );
    msg_amr_get_state.data = (uint8_t)amrState::UNINITIALIZED;

    g_liftJointPosePub = ROSConnectionManager::getInstance().addPub(
        ROSIDL_GET_MSG_TYPE_SUPPORT(sensor_msgs, msg, JointState),
        LIFTKIT_JOINT_STATE_TOPIC
    );

    sensor_msgs__msg__JointState__init(&msg_liftkit_joint_state);
    rosidl_runtime_c__String__Sequence__init(
          &msg_liftkit_joint_state.name,
          LIFTKIT_JOINT_STATE_NUM_JOINTS
    );
    rosidl_runtime_c__double__Sequence__init(
          &msg_liftkit_joint_state.position,
          LIFTKIT_JOINT_STATE_NUM_JOINTS
    );
    rosidl_runtime_c__double__Sequence__init(
          &msg_liftkit_joint_state.velocity,
          LIFTKIT_JOINT_STATE_NUM_JOINTS
    );
    rosidl_runtime_c__double__Sequence__init(
          &msg_liftkit_joint_state.effort,
          LIFTKIT_JOINT_STATE_NUM_JOINTS
    );


    Log("Publishers Initialization Complete");
}

void setup_all_timers() {
    ROSConnectionManager::getInstance().addTimer(
        encoder_timer_callback,
        ENCODER_TIME
    );
    ROSConnectionManager::getInstance().addTimer(
        callback_timer_power_system_battery_reading,
        BATTERY_TIME
    );
    ROSConnectionManager::getInstance().addTimer(
        callback_outgoing_heartbeat_timer,
        HEARTBEAT_TIME_MS
    );
    ROSConnectionManager::getInstance().addTimer(
        callback_timer_amr_get_state,
        AMR_GET_STATE_TIMER_MS
    );
    Log("Timers Initialization Complete");
}

void encoder_timer_callback(rcl_timer_t *timer, int64_t last_call_time) {
    g_rawEncoderPub->publish(&raw_encoder_msg);
}

void callback_outgoing_heartbeat_timer(rcl_timer_t *timer,
                                       int64_t last_call_time) {
    g_heartbeatPub->publish(&msg_outgoing_heartbeat);
}

void callback_timer_power_system_battery_reading(rcl_timer_t *timer,
                                                 int64_t last_call_time) {
    request_power_system_information();

    // Copy data from power system to local message
    custom_msgs__msg__PowerReadings* power_readings = get_power_readings();
    msg_power_system_readings.battery_voltage = power_readings->battery_voltage;
    msg_power_system_readings.battery_current = power_readings->battery_current;
    msg_power_system_readings.battery_soc = power_readings->battery_soc;
    msg_power_system_readings.inverter_voltage = power_readings->inverter_voltage;
    msg_power_system_readings.inverter_soc = power_readings->inverter_soc;

    g_actuationBatteryPub->publish(&msg_power_system_readings);
}

void publish_error_msg(void) {
    g_motorErrorPub->publish(&motor_error_msg);
}

void callback_timer_amr_get_state(rcl_timer_t *timer, int64_t last_call_time) {
  msg_amr_get_state.data = (uint8_t)amr_state;
  g_statePub->publish(&msg_amr_get_state);
}

void callback_liftkit_joint_state_pub() {
    float heightm = liftkit.getCurrentHeightmm() / 1000.0;
    float joint_height = heightm / 2.0;  // Simulate 2 actuators

    rosidl_runtime_c__String__assign(
        &msg_liftkit_joint_state.name.data[0],
        LIFTKIT_JOINT_STATE_NAME_0
    );
    msg_liftkit_joint_state.position.data[0] = joint_height;
    msg_liftkit_joint_state.velocity.data[0] = 0;
    msg_liftkit_joint_state.effort.data[0] = 0;

    rosidl_runtime_c__String__assign(
        &msg_liftkit_joint_state.name.data[1],
        LIFTKIT_JOINT_STATE_NAME_1
    );
    msg_liftkit_joint_state.position.data[1] = joint_height;
    msg_liftkit_joint_state.velocity.data[1] = 0;
    msg_liftkit_joint_state.effort.data[1] = 0;

    rcl_time_point_value_t currentTimePoint = rmw_uros_epoch_nanos();
    msg_liftkit_joint_state.header.stamp.sec = currentTimePoint / 1'000'000'000;
    msg_liftkit_joint_state.header.stamp.nanosec = currentTimePoint % 1'000'000'000;

    g_liftJointPosePub->publish(&msg_liftkit_joint_state);
}

// ------------------- //
// Service
// ------------------- //

using LiftkitRequest_t = custom_msgs__srv__MoveLift_Request;
using LiftkitResponse_t = custom_msgs__srv__MoveLift_Response;

LiftkitRequest_t liftkitServiceRequestBuffer;
LiftkitResponse_t liftkitServiceResponseBuffer;

void liftkit_service_callback(const void * req, void * res) {
    const auto * const request = reinterpret_cast<const LiftkitRequest_t*>(req);
    Log("Request: ");
    // m to mm transform
    auto height = static_cast<int32_t>(request->position.z * 1000);
    Log(height);

    auto * response = reinterpret_cast<LiftkitResponse_t*>(res);
    response->success = liftkit.m_goToHeightmm(height);
    rosidl_runtime_c__String__assign(&response->message, "Done");
}


void setup_all_services()
{
    custom_msgs__srv__MoveLift_Request__init(
            &liftkitServiceRequestBuffer
    );
    custom_msgs__srv__MoveLift_Response__init(
            &liftkitServiceResponseBuffer
    );
    ROSConnectionManager::getInstance().addService(
        ROSIDL_GET_SRV_TYPE_SUPPORT(custom_msgs, srv, MoveLift),
        LIFTKIT_SERVICE_TOPIC,
        &liftkitServiceRequestBuffer,
        &liftkitServiceResponseBuffer,
        liftkit_service_callback
    );
}


// ------------------------------------------------------ //
// Subscribers
// ------------------------------------------------------ //
void setup_all_subscribers() {
    ROSConnectionManager::getInstance().addSub(
        ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Float32MultiArray),
        WHEEL_POSE_CONTROL_TOPIC,
        &wheel_pose_control_msg,
        wheel_pose_control_callback,
        false
    );
    wheel_pose_control_msg.data.capacity = 6;
    wheel_pose_control_msg.data.size = 6;
    static std::array<float, 6> wheelPoseBuffer;
    wheel_pose_control_msg.data.data = wheelPoseBuffer.data();

    ROSConnectionManager::getInstance().addSub(
        ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt8),
        AMR_POWER_TOPIC,
        &amr_power_msg,
        amr_power_callback
    );

    ROSConnectionManager::getInstance().addSub(
        ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt8),
        HEARTBEAT_TOPIC,
        &msg_incoming_heartbeat,
        callback_incoming_heartbeat
    );

    ROSConnectionManager::getInstance().addSub(
        ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt8),
        AMR_SET_STATE_TOPIC,
        &msg_amr_set_state,
        callback_amr_set_state
    );

    Log("Subscribers Initialization Complete");
}


void wheel_pose_control_callback(const void *msgin) {
  RCLC_UNUSED(msgin);
  g_wheelPoseControlMsgRecieved = true;
}

void amr_power_callback(const void *msgin) {
  RCLC_UNUSED(msgin);
  Log("Emergency Stop Requested");
  amr_engage_brakes();
}

void callback_incoming_heartbeat(const void *msgin) {
  RCLC_UNUSED(msgin);
  if (msg_incoming_heartbeat.data ==
      (uint8_t)ROS_DEVICE_HEARTBEAT::AGX_ORIN_ROS_HEARTBEAT_ID)
  {
    Log("HB");
  }
}

void callback_amr_set_state(const void *msgin) {
  RCLC_UNUSED(msgin);
  g_flagStateChangeRequested = true;
  Log("Requested State: ");
  Log(msg_amr_set_state.data);
}

const char* local_ip = "*************";
size_t agent_port = 8888;
#ifdef __linux__
const char* agent_ip = "0.0.0.0";
#else
const char* agent_ip = "***********";
#endif

void setup() {
#ifndef __linux__
  Serial.begin(926100);
#endif

    ROSConnectionManager::getInstance().initialize(
        NODE_NAME,
        local_ip,
        agent_ip,
        agent_port
    );

    Log("ROS Enable in Process!");

    setup_all_publishers();
    setup_all_subscribers();
    setup_all_services();
    setup_all_timers();

    Log("ROS Enable Complete!");



  if (CanDevice::getActuationCan()->connect()) {
      Log("can1 ok");
  } else {
    Log("Error can1 setup");
  }
  if (CanDevice::getPowerCan()->connect()) {
    Log("can2 ok");
  } else {
    Log("Error can2 setup");
  }

  
  Log("CAN Initialization Complete!");
  Log("Ready to Use!");
}


void updateEncoderReadTime(
        uint32_t &dT,
        uint64_t &timeStamp,
        uint64_t &startTime) {
    auto currentTime = rmw_uros_epoch_millis();
    dT = static_cast<uint32_t>(currentTime - startTime);
    timeStamp = currentTime;
    startTime = currentTime;
}

union CanRecvRegister
{
    struct
    {
        uint8_t frontTravel: 1;
        uint8_t frontSteer: 1;
        uint8_t rearLeftTravel: 1;
        uint8_t rearLeftSteer: 1;
        uint8_t rearRightTravel: 1;
        uint8_t rearRightSteer: 1;
        uint8_t liftSteer: 1;
    };
    uint8_t reg;
};

const auto CAN_ACTUATION_READ_TIMEOUT_MS = 10;
const auto CAN_RECV_REGISTER_FULL = 0x7F;
CanRecvRegister g_encoderReadFlags;
CanRecvRegister g_errorReadFlags;
bool g_encoderRequested{false};
bool g_errorRequested{false};

void amr_active_state() {
  if (g_wheelPoseControlMsgRecieved == true) {
    for (uint8_t idx = 0; idx < 3; idx++) {
      float travel_motor_speed_rpm =
          AMR_LINEAR_VELOCITY_MpS_TO_ANGULAR_VELOCITY_RPM(
              wheel_pose_control_msg.data.data[2 * idx]);
      float steer_angle_degrees =
          wheel_pose_control_msg.data.data[(2 * idx) + 1];
      g_wheels[idx]->m_setTravelSpeed_RPM(travel_motor_speed_rpm);
      g_wheels[idx]->m_setAbsoluteSteerToAngle_Degrees(steer_angle_degrees);
    }
    g_wheelPoseControlMsgRecieved = false;
  }

    auto currentTime = rmw_uros_epoch_millis();
    if (currentTime - encoder_read_time > ENCODER_TIME) {
        for (auto wheel : g_wheels) {
            wheel->m_requestEncoderReadings();
        }
        liftkit.requestEncoderReadings();
        encoder_read_time = currentTime;
        g_encoderRequested = true;
        g_encoderReadFlags.reg = 0u;
    }
    if (currentTime - error_read_time > ERROR_TIME) {
        for (auto wheel : g_wheels) {
            wheel->m_requestErrorReadings();
        }
        error_read_time = currentTime;
        g_errorRequested = true;
        g_errorReadFlags.reg = 0u;
    }

    if (currentTime - liftkit_encoder_read_time > LIFTKIT_HEIGHT_PUBLISH_TIME) {
        callback_liftkit_joint_state_pub();
        liftkit_encoder_read_time = currentTime;
    }

    if (currentTime - sync_time > SYNC_TIME) {
        if (RMW_RET_OK != rmw_uros_sync_session(100)) {
          Log("[UROS] Sync session failed");
        }
        sync_time = currentTime;
    }

    static auto frontEncoderReadStart = static_cast<uint64_t>(
            rmw_uros_epoch_millis()
    );
    static auto rearLeftEncoderReadStart = frontEncoderReadStart;
    static auto rearRightEncoderReadStart = frontEncoderReadStart;

    if (g_encoderRequested)
    {
        auto startTime = rmw_uros_epoch_millis();
        while (true)
        {
            if (CanDevice::getActuationCan()->dataAvailable())
            {
                auto message = CanDevice::getActuationCan()->getRecvDataBuffer();
                auto id = CanDevice::getActuationCan()->getRecvId();
                const uint16_t message_idx = static_cast<uint16_t>(
                    CanUtils::interpret_can_bytes_to_decimal_big_endian(
                        1.0f,
                        0.0f,
                        2,
                        message[1],
                        message[2]
                    )
                );
                if (message_idx != POS_ACTUAL_IDX)
                {
                    continue;
                }
                switch(id)
                {
                    case RECIEVE_MESSAGE_CAN_IDENTIFIER
                        + FRONT_WHEEL_TRAVEL_MOTOR_NODE_ID:
                    {
                        updateEncoderReadTime(
                            raw_encoder_msg.front_dt,
                            raw_encoder_msg.front_timestamp,
                            frontEncoderReadStart
                        );
                        frontWheel.parseTravelEncoderReading(
                            message[4],
                            message[5],
                            message[6],
                            message[7],
                            raw_encoder_msg.front_travel
                        );
                        g_encoderReadFlags.frontTravel = 1;
                        break;
                    }
                    case RECIEVE_MESSAGE_CAN_IDENTIFIER
                        + FRONT_WHEEL_STEER_MOTOR_NODE_ID:
                    {
                        frontWheel.parseSteerEncoderReading(
                            message[4],
                            message[5],
                            message[6],
                            message[7],
                            raw_encoder_msg.front_steer
                        );
                        g_encoderReadFlags.frontSteer = 1;
                        break;
                    }
                    case RECIEVE_MESSAGE_CAN_IDENTIFIER
                        + REAR_LEFT_WHEEL_TRAVEL_MOTOR_NODE_ID:
                    {
                        updateEncoderReadTime(
                            raw_encoder_msg.rear_left_dt,
                            raw_encoder_msg.rear_left_timestamp,
                            rearLeftEncoderReadStart
                        );
                        rearLeftWheel.parseTravelEncoderReading(
                            message[4],
                            message[5],
                            message[6],
                            message[7],
                            raw_encoder_msg.rear_left_travel
                        );
                        g_encoderReadFlags.rearLeftTravel = 1;
                        break;
                    }
                    case RECIEVE_MESSAGE_CAN_IDENTIFIER
                        + REAR_LEFT_WHEEL_STEER_MOTOR_NODE_ID:
                    {
                        rearLeftWheel.parseSteerEncoderReading(
                            message[4],
                            message[5],
                            message[6],
                            message[7],
                            raw_encoder_msg.rear_left_steer
                        );
                        g_encoderReadFlags.rearLeftSteer = 1;
                        break;
                    }
                    case RECIEVE_MESSAGE_CAN_IDENTIFIER
                        + REAR_RIGHT_WHEEL_TRAVEL_MOTOR_NODE_ID:
                    {
                        updateEncoderReadTime(
                            raw_encoder_msg.rear_right_dt,
                            raw_encoder_msg.rear_right_timestamp,
                            rearRightEncoderReadStart
                        );
                        rearRightWheel.parseTravelEncoderReading(
                            message[4],
                            message[5],
                            message[6],
                            message[7],
                            raw_encoder_msg.rear_right_travel
                        );
                        g_encoderReadFlags.rearRightTravel = 1;
                        break;
                    }
                    case RECIEVE_MESSAGE_CAN_IDENTIFIER
                        + REAR_RIGHT_WHEEL_STEER_MOTOR_NODE_ID:
                    {
                        rearRightWheel.parseSteerEncoderReading(
                            message[4],
                            message[5],
                            message[6],
                            message[7],
                            raw_encoder_msg.rear_right_steer
                        );
                        g_encoderReadFlags.rearRightSteer = 1;
                        break;
                    }
                    case RECIEVE_MESSAGE_CAN_IDENTIFIER
                        + LIFTKIT_STEER_MOTOR_NODE_ID:
                    {
                        liftkit.parseEncoderValue(4, &message[4]);
                        g_encoderReadFlags.liftSteer = 1;
                        break;
                    }
                    default:
                    {
                        break;
                    }
                }   // switch
            }   // if data available
            if (rmw_uros_epoch_millis() - startTime > CAN_ACTUATION_READ_TIMEOUT_MS)
            {
                break;
            }
            else if (CAN_RECV_REGISTER_FULL == g_encoderReadFlags.reg)
            {
                break;
            }
        } // while(true)
        g_encoderRequested = false;
    } // if encoder requested

    if (g_errorRequested)
    {
        auto startTime = rmw_uros_epoch_millis();
        // We don't ask for lift error yet
        g_errorReadFlags.liftSteer = 1;
        while (true)
        {
            if (CanDevice::getActuationCan()->dataAvailable())
            {
                auto message = CanDevice::getActuationCan()->getRecvDataBuffer();
                auto id = CanDevice::getActuationCan()->getRecvId();
                const uint16_t message_idx = static_cast<uint16_t>(
                    CanUtils::interpret_can_bytes_to_decimal_big_endian(
                        1.0f,
                        0.0f,
                        2,
                        message[1],
                        message[2]
                    )
                );
                if (message_idx != ERROR_STATE_IDX)
                {
                    continue;
                }
                switch(id)
                {
                    case RECIEVE_MESSAGE_CAN_IDENTIFIER
                        + FRONT_WHEEL_TRAVEL_MOTOR_NODE_ID:
                    {
                        if(frontWheel.parseTravelMotorError(
                                message[4],
                                message[5],
                                motor_error_msg.data.data[0]
                            ))
                        {
                            publish_error_msg();
                        }
                        g_errorReadFlags.frontTravel = 1;
                        break;
                    }
                    case RECIEVE_MESSAGE_CAN_IDENTIFIER
                        + FRONT_WHEEL_STEER_MOTOR_NODE_ID:
                    {
                        if(frontWheel.parseSteerMotorError(
                                message[4],
                                message[5],
                                motor_error_msg.data.data[1]
                            ))
                        {
                            publish_error_msg();
                        }
                        g_errorReadFlags.frontSteer = 1;
                        break;
                    }
                    case RECIEVE_MESSAGE_CAN_IDENTIFIER
                        + REAR_LEFT_WHEEL_TRAVEL_MOTOR_NODE_ID:
                    {
                        if(rearLeftWheel.parseTravelMotorError(
                                message[4],
                                message[5],
                                motor_error_msg.data.data[0]
                            ))
                        {
                            publish_error_msg();
                        }
                        g_errorReadFlags.rearLeftTravel = 1;
                        break;
                    }
                    case RECIEVE_MESSAGE_CAN_IDENTIFIER
                        + REAR_LEFT_WHEEL_STEER_MOTOR_NODE_ID:
                    {
                        if(rearLeftWheel.parseSteerMotorError(
                                message[4],
                                message[5],
                                motor_error_msg.data.data[1]
                            ))
                        {
                            publish_error_msg();
                        }
                        g_errorReadFlags.rearLeftSteer = 1;
                        break;
                    }
                    case RECIEVE_MESSAGE_CAN_IDENTIFIER
                        + REAR_RIGHT_WHEEL_TRAVEL_MOTOR_NODE_ID:
                    {
                        if(rearRightWheel.parseTravelMotorError(
                                message[4],
                                message[5],
                                motor_error_msg.data.data[0]
                            ))
                        {
                            publish_error_msg();
                        }
                        g_errorReadFlags.rearRightTravel = 1;
                        break;
                    }
                    case RECIEVE_MESSAGE_CAN_IDENTIFIER
                        + REAR_RIGHT_WHEEL_STEER_MOTOR_NODE_ID:
                    {
                        if(rearRightWheel.parseSteerMotorError(
                                message[4],
                                message[5],
                                motor_error_msg.data.data[1]
                            ))
                        {
                            publish_error_msg();
                        }
                        g_errorReadFlags.rearRightSteer = 1;
                        break;
                    }
                    default:
                    {
                        break;
                    }
                }   // switch
            }   // if data available
            if (rmw_uros_epoch_millis() - startTime > CAN_ACTUATION_READ_TIMEOUT_MS)
            {
                break;
            }
            if (CAN_RECV_REGISTER_FULL == g_errorReadFlags.reg)
            {
                break;
            }
        } // while(true)
        g_errorRequested = false;
    } // if error requested
}

void loop() {
    // User Control
  if (g_flagStateChangeRequested) {
      Log("State change requested");
    switch ((amrState)msg_amr_set_state.data) {
    case amrState::INITIALIZE:
      amr_state = amrState::INITIALIZING;
      break;
    case amrState::UNINITIALIZE:
      amr_state = amrState::UNINITIALIZING;
      break;
    default:
      Log("Invalid State Requested By User: ");
      Log((uint8_t)msg_amr_set_state.data);
      break;
    }
    g_flagStateChangeRequested = false;
  }

  // State Machine for AMR Control
  switch (amr_state) {
  case amrState::UNINITIALIZED:
    break;
  case amrState::INITIALIZE:
    break;
  case amrState::INITIALIZING:
    initialize_amr();
    liftkit.m_initialize();
    amr_state = amrState::ACTIVE;
    break;
  case amrState::ACTIVE:
    amr_active_state();
    break;
  case amrState::ERROR:
    break;
  case amrState::UNINITIALIZE:
    break;
  case amrState::UNINITIALIZING:
    set_tower_light_state(eTowerLightState::INITIALIZING_MODE);
    amr_engage_brakes();
    liftkit.m_triggerBrakes();
    motorSSR.disable();
    amr_state = amrState::UNINITIALIZED;
    break;
  default:
    Log("Invalid State: ");
    Log((uint8_t)amr_state);
    break;
  }

  if (CanDevice::getPowerCan()->dataAvailable()) {
    // Receive the message
    auto message = CanDevice::getPowerCan()->getRecvDataBuffer();
    parse_power_system_message(
        CanDevice::getPowerCan()->getRecvId(),
        message,
        get_power_readings()
    );
  }

    [[maybe_unused]] auto loopTime = ROSConnectionManager::getInstance().spinSome(10ms);
    if (ROSConnectionManager::getInstance().isInitialized())
    {
        static bool calledFlag = false;
        if (not calledFlag)
        {
            initializeIo();
            calledFlag = true;
        }
    }
}

#ifdef __linux__
#include <csignal>

bool g_keepRunning = true;

void signalHandler(int sig)  {
    Log("SIG received");
    g_keepRunning = false;
}

auto main(int argc, char ** argv) -> int
{
    signal(SIGINT, signalHandler);
    setup();
    while (g_keepRunning)
    {
        loop();
    }
    Log("Exiting...");
}
#endif


